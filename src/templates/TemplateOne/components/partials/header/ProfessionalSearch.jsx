import React, { useState, useRef, useEffect } from "react";
import { Icon } from "@iconify/react";
import { useNavigate } from "react-router-dom";
import { ASSET_URL } from "@/config";
import api from "@/server/api";

const ProfessionalSearch = ({ className = "", placeholder = "Search courses...", onSearchStateChange }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const [query, setQuery] = useState("");
  const [searchResults, setSearchResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [debounceTimeout, setDebounceTimeout] = useState(null);

  const searchRef = useRef(null);
  const inputRef = useRef(null);
  const navigate = useNavigate();

  // Handle search API call
  const searchCourse = async (searchQuery) => {
    if (!searchQuery.trim()) {
      setSearchResults([]);
      return;
    }

    setLoading(true);
    try {
      const response = await api.get(`/search-course?query=${encodeURIComponent(searchQuery)}`);
      if (response?.data?.data) {
        setSearchResults(response.data.data);
      } else {
        setSearchResults([]);
      }
    } catch (error) {
      console.error("Search error:", error);
      setSearchResults([]);
    } finally {
      setLoading(false);
    }
  };

  // Handle input change with debounce
  const handleInputChange = (value) => {
    setQuery(value);
    
    // Clear previous timeout
    if (debounceTimeout) {
      clearTimeout(debounceTimeout);
    }
    
    // Set new timeout
    const newTimeout = setTimeout(() => {
      if (value.trim().length >= 2) {
        searchCourse(value.trim());
      } else {
        setSearchResults([]);
      }
    }, 300);
    
    setDebounceTimeout(newTimeout);
  };

  // Handle search submission
  const handleSearch = () => {
    if (query.trim()) {
      navigate(`/search?query=${encodeURIComponent(query.trim())}`);
      setIsOpen(false);
      setQuery("");
    }
  };

  // Handle key press
  const handleKeyPress = (e) => {
    if (e.key === "Enter") {
      handleSearch();
    } else if (e.key === "Escape") {
      setIsOpen(false);
      setQuery("");
    }
  };

  // Handle click outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (searchRef.current && !searchRef.current.contains(event.target)) {
        setIsOpen(false);
        setIsFocused(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Focus input when opened
  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  // Notify parent component about search state
  useEffect(() => {
    if (onSearchStateChange) {
      onSearchStateChange(isFocused || isOpen);
    }
  }, [isFocused, isOpen, onSearchStateChange]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (debounceTimeout) {
        clearTimeout(debounceTimeout);
      }
    };
  }, [debounceTimeout]);

  return (
    <div ref={searchRef} className={`relative transition-all duration-300 ${className} ${
      isFocused || isOpen ? 'w-full' : 'w-56'
    }`}>
      {/* Search Input */}
      <div className="relative">
        <div
          className={`flex items-center bg-white border-2 rounded-xl transition-all duration-300 ${
            isOpen || isFocused
              ? 'border-sky-500 shadow-lg ring-4 ring-sky-100'
              : 'border-gray-200 hover:border-gray-300 shadow-sm hover:shadow-md'
          }`}
        >
          <div className="pl-4 pr-2">
            <Icon
              icon="mdi:magnify"
              className={`w-5 h-5 transition-colors ${
                isOpen || isFocused ? 'text-sky-600' : 'text-gray-400'
              }`}
            />
          </div>
          <input
            ref={inputRef}
            type="text"
            placeholder={placeholder}
            value={query}
            onChange={(e) => handleInputChange(e.target.value)}
            onKeyDown={handleKeyPress}
            onFocus={() => {
              setIsOpen(true);
              setIsFocused(true);
            }}
            onBlur={() => {
              // Delay blur to allow clicking on results
              setTimeout(() => setIsFocused(false), 150);
            }}
            className={`bg-transparent border-none outline-none text-gray-700 placeholder-gray-400 font-medium transition-all duration-300 ${
              isFocused || isOpen ? 'w-full py-3 pr-3 text-base' : 'w-28 py-3 pr-3 text-sm'
            }`}
          />
          {query && (
            <button
              onClick={() => {
                setQuery("");
                setSearchResults([]);
                inputRef.current?.focus();
              }}
              className="pr-4 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <Icon icon="mdi:close-circle" className="w-5 h-5" />
            </button>
          )}
        </div>

        {/* Search Results Dropdown */}
        {isOpen && (query.length >= 2 || searchResults.length > 0) && (
          <div className="absolute top-full left-0 right-0 mt-3 bg-white border-2 border-gray-100 rounded-2xl shadow-2xl z-50 max-h-96 overflow-hidden backdrop-blur-sm">
            {loading && (
              <div className="flex items-center justify-center py-12">
                <div className="flex flex-col items-center">
                  <div className="w-8 h-8 border-3 border-sky-500 border-t-transparent rounded-full animate-spin mb-3"></div>
                  <span className="text-gray-600 font-medium">Searching courses...</span>
                </div>
              </div>
            )}

            {!loading && query.length >= 2 && searchResults.length === 0 && (
              <div className="flex flex-col items-center justify-center py-12 text-gray-500">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                  <Icon icon="mdi:magnify" className="w-8 h-8 text-gray-400" />
                </div>
                <p className="text-base font-medium text-gray-700 mb-2">No courses found</p>
                <p className="text-sm text-gray-500 mb-4">Try searching for "{query}" in all results</p>
                <button
                  onClick={handleSearch}
                  className="bg-sky-600 hover:bg-sky-700 text-white px-6 py-2.5 rounded-lg text-sm font-medium transition-colors shadow-md hover:shadow-lg"
                >
                  Search All Results →
                </button>
              </div>
            )}

            {!loading && searchResults.length > 0 && (
              <div className="max-h-80 overflow-y-auto">
                <div className="px-6 py-4 border-b border-gray-100 bg-gradient-to-r from-sky-50 to-blue-50">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-semibold text-gray-800">
                        Found {searchResults.length} course{searchResults.length > 1 ? 's' : ''}
                      </p>
                      <p className="text-xs text-gray-500 mt-1">Click to view details</p>
                    </div>
                    <Icon icon="mdi:school" className="w-6 h-6 text-sky-600" />
                  </div>
                </div>

                <div className="py-2">
                  {searchResults.map((item, index) => (
                    <div
                      key={item.id}
                      onClick={() => {
                        navigate(`/course-details/${item.id}`);
                        setIsOpen(false);
                        setQuery("");
                      }}
                      className="flex items-center p-4 mx-2 hover:bg-sky-50 cursor-pointer rounded-xl transition-all duration-200 group"
                    >
                      <div className="relative">
                        <img
                          src={ASSET_URL + item?.thumbnail}
                          alt={item.title}
                          className="w-14 h-14 rounded-xl object-cover flex-shrink-0 shadow-sm"
                        />
                        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 rounded-xl transition-all duration-200"></div>
                      </div>
                      <div className="ml-4 flex-1 min-w-0">
                        <h4 className="text-sm font-semibold text-gray-900 truncate group-hover:text-sky-700 transition-colors">
                          {item.title}
                        </h4>
                        <p className="text-xs text-gray-500 mt-1 flex items-center">
                          <Icon icon="mdi:tag" className="w-3 h-3 mr-1" />
                          {item.category_name || "Course"}
                        </p>
                      </div>
                      <div className="flex items-center text-gray-400 group-hover:text-sky-600 transition-colors">
                        <Icon icon="mdi:arrow-right" className="w-5 h-5" />
                      </div>
                    </div>
                  ))}
                </div>

                {searchResults.length >= 5 && (
                  <div className="p-4 border-t border-gray-100 bg-gray-50">
                    <button
                      onClick={handleSearch}
                      className="w-full bg-white hover:bg-sky-50 text-sky-600 hover:text-sky-700 border-2 border-sky-200 hover:border-sky-300 text-sm font-semibold py-3 rounded-xl transition-all duration-200 shadow-sm hover:shadow-md"
                    >
                      View All Results for "{query}" →
                    </button>
                  </div>
                )}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default ProfessionalSearch;
