import React, { useState, useRef, useEffect } from "react";
import { Icon } from "@iconify/react";
import { useNavigate } from "react-router-dom";
import { ASSET_URL } from "@/config";
import api from "@/server/api";

const ProfessionalSearch = ({ className = "", placeholder = "Search courses...", onSearchStateChange }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const [query, setQuery] = useState("");
  const [searchResults, setSearchResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [debounceTimeout, setDebounceTimeout] = useState(null);

  const searchRef = useRef(null);
  const inputRef = useRef(null);
  const navigate = useNavigate();

  const searchCourse = async (searchQuery) => {
    if (!searchQuery.trim()) {
      setSearchResults([]);
      return;
    }

    setLoading(true);
    try {
      const response = await api.get(`/search-course?query=${encodeURIComponent(searchQuery)}`);
      if (response?.data?.data) {
        setSearchResults(response.data.data);
      } else {
        setSearchResults([]);
      }
    } catch (error) {
      console.error("Search error:", error);
      setSearchResults([]);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (value) => {
    setQuery(value);
    
    if (debounceTimeout) {
      clearTimeout(debounceTimeout);
    }
    
    const newTimeout = setTimeout(() => {
      if (value.trim().length >= 2) {
        searchCourse(value.trim());
      } else {
        setSearchResults([]);
      }
    }, 300);
    
    setDebounceTimeout(newTimeout);
  };

  const handleSearch = () => {
    if (query.trim()) {
      navigate(`/search?query=${encodeURIComponent(query.trim())}`);
      setIsOpen(false);
      setQuery("");
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === "Enter") {
      handleSearch();
    } else if (e.key === "Escape") {
      setIsOpen(false);
      setQuery("");
    }
  };

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (searchRef.current && !searchRef.current.contains(event.target)) {
        setIsOpen(false);
        setIsFocused(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  useEffect(() => {
    if (onSearchStateChange) {
      onSearchStateChange(isFocused || isOpen);
    }
  }, [isFocused, isOpen, onSearchStateChange]);

  useEffect(() => {
    return () => {
      if (debounceTimeout) {
        clearTimeout(debounceTimeout);
      }
    };
  }, [debounceTimeout]);

  return (
    <div 
      ref={searchRef} 
      className={`relative ${className} ${
        isFocused || isOpen ? 'w-full' : 'w-64'
      } transition-all duration-300 ease-[cubic-bezier(0.4,0,0.2,1)]`}
    >
      {/* Search Input */}
      <div className="relative">
        <div
          className={`flex items-center bg-white border rounded-lg transition-all duration-300 ${
            isOpen || isFocused
              ? 'border-primary-500 shadow-lg ring-2 ring-primary-100'
              : 'border-gray-200 hover:border-gray-300 shadow-sm'
          }`}
        >
          <div className="pl-3 pr-2 text-gray-400">
            <Icon
              icon="mdi:magnify"
              className={`w-5 h-5 transition-colors ${
                isOpen || isFocused ? 'text-primary-600' : 'text-gray-400'
              }`}
            />
          </div>
          <input
            ref={inputRef}
            type="text"
            placeholder={placeholder}
            value={query}
            onChange={(e) => handleInputChange(e.target.value)}
            onKeyDown={handleKeyPress}
            onFocus={() => {
              setIsOpen(true);
              setIsFocused(true);
            }}
            onBlur={() => {
              setTimeout(() => setIsFocused(false), 150);
            }}
            className={`bg-transparent border-none outline-none text-gray-800 placeholder-gray-400 font-medium transition-all duration-300 ${
              isFocused || isOpen ? 'w-full py-2.5 pr-3 text-sm' : 'w-18 py-2.5 pr-3 text-sm'
            }`}
          />
          {query && (
            <button
              onClick={() => {
                setQuery("");
                setSearchResults([]);
                inputRef.current?.focus();
              }}
              className="pr-3 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <Icon icon="mdi:close-circle" className="w-5 h-5" />
            </button>
          )}
        </div>

        {/* Search Results Dropdown */}
        {isOpen && (query.length >= 2 || searchResults.length > 0) && (
          <div className="absolute top-full left-0 right-0 mt-1.5 bg-white border border-gray-200 rounded-lg shadow-xl z-50 max-h-[28rem] overflow-hidden backdrop-blur-sm animate-fade-in">
            {loading && (
              <div className="flex items-center justify-center py-8">
                <div className="flex flex-col items-center space-y-3">
                  <div className="w-8 h-8 border-2 border-primary-500 border-t-transparent rounded-full animate-spin"></div>
                  <span className="text-sm font-medium text-gray-600">Searching courses...</span>
                </div>
              </div>
            )}

            {!loading && query.length >= 2 && searchResults.length === 0 && (
              <div className="flex flex-col items-center justify-center p-6 text-center">
                <div className="w-12 h-12 bg-gray-50 rounded-full flex items-center justify-center mb-4">
                  <Icon icon="mdi:magnify" className="w-6 h-6 text-gray-400" />
                </div>
                <p className="text-sm font-medium text-gray-700 mb-1">No courses found</p>
                <p className="text-xs text-gray-500 mb-4">Try different keywords</p>
                <button
                  onClick={handleSearch}
                  className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-xs font-medium transition-colors shadow-sm"
                >
                  Search All Results
                </button>
              </div>
            )}

            {!loading && searchResults.length > 0 && (
              <div className="max-h-[28rem] overflow-y-auto divide-y divide-gray-100">
                <div className="px-4 py-3 bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-xs font-semibold text-gray-500 uppercase tracking-wider">
                        Search Results
                      </p>
                      <p className="text-sm text-gray-700 mt-0.5">
                        {searchResults.length} course{searchResults.length !== 1 ? 's' : ''} found
                      </p>
                    </div>
                    <Icon icon="mdi:school" className="w-5 h-5 text-primary-500" />
                  </div>
                </div>

                <div className="divide-y divide-gray-100">
                  {searchResults.map((item) => (
                    <div
                      key={item.id}
                      onClick={() => {
                        navigate(`/course-details/${item.id}`);
                        setIsOpen(false);
                        setQuery("");
                      }}
                      className="flex items-center p-3 hover:bg-gray-50 cursor-pointer transition-colors duration-150"
                    >
                      <div className="flex-shrink-0 w-10 h-10 rounded-lg bg-gray-100 overflow-hidden">
                        <img
                          src={ASSET_URL + item?.thumbnail}
                          alt={item.title}
                          className="w-full h-full object-cover"
                        />
                      </div>
                      <div className="ml-3 flex-1 min-w-0">
                        <h4 className="text-sm font-medium text-gray-900 truncate">
                          {item.title}
                        </h4>
                        <div className="flex items-center mt-1">
                          <span className="text-xs text-gray-500 inline-flex items-center">
                            <Icon icon="mdi:tag" className="w-3 h-3 mr-1" />
                            {item.category_name || "Course"}
                          </span>
                        </div>
                      </div>
                      <Icon 
                        icon="mdi:chevron-right" 
                        className="w-5 h-5 text-gray-400 ml-2" 
                      />
                    </div>
                  ))}
                </div>

                {searchResults.length >= 5 && (
                  <div className="p-3 bg-gray-50 border-t border-gray-100">
                    <button
                      onClick={handleSearch}
                      className="w-full bg-white hover:bg-gray-50 text-primary-600 hover:text-primary-700 border border-gray-200 text-xs font-medium py-2 rounded-md transition-colors duration-200"
                    >
                      View all {searchResults.length} results for "{query}"
                    </button>
                  </div>
                )}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default ProfessionalSearch;