import React, { useState, useRef, useEffect } from "react";
import { Icon } from "@iconify/react";
import { useNavigate } from "react-router-dom";
import { ASSET_URL } from "@/config";
import api from "@/server/api";

const ProfessionalSearch = ({ className = "", placeholder = "Search courses...", onSearchStateChange }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const [query, setQuery] = useState("");
  const [searchResults, setSearchResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [debounceTimeout, setDebounceTimeout] = useState(null);

  const searchRef = useRef(null);
  const inputRef = useRef(null);
  const navigate = useNavigate();

  // Handle search API call
  const searchCourse = async (searchQuery) => {
    if (!searchQuery.trim()) {
      setSearchResults([]);
      return;
    }

    setLoading(true);
    try {
      const response = await api.get(`/search-course?query=${encodeURIComponent(searchQuery)}`);
      if (response?.data?.data) {
        setSearchResults(response.data.data);
      } else {
        setSearchResults([]);
      }
    } catch (error) {
      console.error("Search error:", error);
      setSearchResults([]);
    } finally {
      setLoading(false);
    }
  };

  // Handle input change with debounce
  const handleInputChange = (value) => {
    setQuery(value);
    
    // Clear previous timeout
    if (debounceTimeout) {
      clearTimeout(debounceTimeout);
    }
    
    // Set new timeout
    const newTimeout = setTimeout(() => {
      if (value.trim().length >= 2) {
        searchCourse(value.trim());
      } else {
        setSearchResults([]);
      }
    }, 300);
    
    setDebounceTimeout(newTimeout);
  };

  // Handle search submission
  const handleSearch = () => {
    if (query.trim()) {
      navigate(`/search?query=${encodeURIComponent(query.trim())}`);
      setIsOpen(false);
      setQuery("");
    }
  };

  // Handle key press
  const handleKeyPress = (e) => {
    if (e.key === "Enter") {
      handleSearch();
    } else if (e.key === "Escape") {
      setIsOpen(false);
      setQuery("");
    }
  };

  // Handle click outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (searchRef.current && !searchRef.current.contains(event.target)) {
        setIsOpen(false);
        setIsFocused(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Focus input when opened
  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  // Notify parent component about search state
  useEffect(() => {
    if (onSearchStateChange) {
      onSearchStateChange(isFocused || isOpen);
    }
  }, [isFocused, isOpen, onSearchStateChange]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (debounceTimeout) {
        clearTimeout(debounceTimeout);
      }
    };
  }, [debounceTimeout]);

  return (
    <div ref={searchRef} className={`relative transition-all duration-300 ${className} ${
      isFocused || isOpen ? 'w-80 lg:w-96' : 'w-56'
    }`}>
      {/* Search Input */}
      <div className="relative">
        <div
          className={`flex items-center bg-gray-50 hover:bg-gray-100 border border-gray-200 rounded-lg transition-all duration-300 ${
            isOpen || isFocused ? 'ring-2 ring-sky-500 border-sky-500 bg-white shadow-lg' : 'hover:border-gray-300'
          }`}
        >
          <div className="pl-3 pr-2">
            <Icon
              icon="mdi:magnify"
              className={`w-4 h-4 transition-colors ${
                isOpen || isFocused ? 'text-sky-600' : 'text-gray-400'
              }`}
            />
          </div>
          <input
            ref={inputRef}
            type="text"
            placeholder={placeholder}
            value={query}
            onChange={(e) => handleInputChange(e.target.value)}
            onKeyDown={handleKeyPress}
            onFocus={() => {
              setIsOpen(true);
              setIsFocused(true);
            }}
            onBlur={() => {
              // Delay blur to allow clicking on results
              setTimeout(() => setIsFocused(false), 150);
            }}
            className={`bg-transparent border-none outline-none text-gray-700 placeholder-gray-500 text-sm transition-all duration-300 ${
              isFocused || isOpen ? 'w-full py-2.5 pr-3' : 'w-28 py-2.5 pr-3'
            }`}
          />
          {query && (
            <button
              onClick={() => {
                setQuery("");
                setSearchResults([]);
                inputRef.current?.focus();
              }}
              className="pr-3 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <Icon icon="mdi:close" className="w-4 h-4" />
            </button>
          )}
        </div>

        {/* Search Results Dropdown */}
        {isOpen && (query.length >= 2 || searchResults.length > 0) && (
          <div className="absolute top-full left-0 right-0 mt-2 bg-white border border-gray-200 rounded-lg shadow-xl z-50 max-h-96 overflow-hidden">
            {loading && (
              <div className="flex items-center justify-center py-8">
                <Icon icon="mdi:loading" className="w-6 h-6 text-sky-600 animate-spin mr-2" />
                <span className="text-gray-600">Searching...</span>
              </div>
            )}

            {!loading && query.length >= 2 && searchResults.length === 0 && (
              <div className="flex flex-col items-center justify-center py-8 text-gray-500">
                <Icon icon="mdi:magnify" className="w-12 h-12 mb-2 text-gray-300" />
                <p className="text-sm">No courses found for "{query}"</p>
                <button
                  onClick={handleSearch}
                  className="mt-2 text-sky-600 hover:text-sky-700 text-sm font-medium"
                >
                  Search all results →
                </button>
              </div>
            )}

            {!loading && searchResults.length > 0 && (
              <div className="max-h-80 overflow-y-auto">
                <div className="px-4 py-2 border-b border-gray-100 bg-gray-50">
                  <p className="text-xs font-medium text-gray-600 uppercase tracking-wide">
                    Courses ({searchResults.length})
                  </p>
                </div>
                {searchResults.map((item) => (
                  <div
                    key={item.id}
                    onClick={() => {
                      navigate(`/course-details/${item.id}`);
                      setIsOpen(false);
                      setQuery("");
                    }}
                    className="flex items-center p-4 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0 transition-colors"
                  >
                    <img
                      src={ASSET_URL + item?.thumbnail}
                      alt={item.title}
                      className="w-12 h-12 rounded-lg object-cover flex-shrink-0"
                    />
                    <div className="ml-3 flex-1 min-w-0">
                      <h4 className="text-sm font-medium text-gray-900 truncate">
                        {item.title}
                      </h4>
                      <p className="text-xs text-gray-500 mt-1">
                        {item.category_name || "Course"}
                      </p>
                    </div>
                    <Icon icon="mdi:arrow-right" className="w-4 h-4 text-gray-400 flex-shrink-0" />
                  </div>
                ))}
                
                {searchResults.length >= 5 && (
                  <div className="p-3 border-t border-gray-100 bg-gray-50">
                    <button
                      onClick={handleSearch}
                      className="w-full text-center text-sky-600 hover:text-sky-700 text-sm font-medium py-2 hover:bg-white rounded-md transition-colors"
                    >
                      View all results for "{query}" →
                    </button>
                  </div>
                )}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default ProfessionalSearch;
