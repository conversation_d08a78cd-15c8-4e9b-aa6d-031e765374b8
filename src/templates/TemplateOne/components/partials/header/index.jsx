import React, { useState, useEffect, useRef } from "react";
import { Icon } from "@iconify/react";
import eduLogo from "@/assets/images/logo/edu_logo.png";
import { useNavigate, Link } from "react-router-dom";
import Button from "@/components/ui/Button";
import useFetch from "@/hooks/useFetch";
import userImg from "@/assets/images/all-img/user5.jpeg";
import { useSelector, useDispatch } from "react-redux";
import { handleLogout } from "../../../pages/auth/common/store";
import { ASSET_URL } from "@/config";
import api from "@/server/api";
import MenuDropdown from "../../ui/MenuDropdown";
import NavBar from "../../ui/NavBar";
import ProfessionalSearch from "./ProfessionalSearch";
import Loading from "../../Loading";
import ProfileDropdown from "../../ui/ProfileDropdown";
import dashboardImg from '@/assets/images/svg/dashboard.svg';
import myCoursesImg from '@/assets/images/svg/myCourses.svg';
import profileImg from '@/assets/images/svg/profile.svg';
import assignmentImg from '@/assets/images/svg/assignment.svg';
import supportImg from '@/assets/images/svg/support.svg';
import Payment from '@/assets/images/svg/payment.svg';
import { useTranslation } from "react-i18next";
import Notification from "./Notification";

const Header = ({ organization }) => {
  const [activeDropdown, setActiveDropdown] = useState(null);
  const [isSearchActive, setIsSearchActive] = useState(false);
  const { isAuth } = useSelector((state) => state.auth);
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const { t, i18n } = useTranslation();
  const {
    data: menuItems,
    isLoading,
  } = useFetch({
    queryKey: "menu-list",
    endPoint: "menu-list",
  });


  const mobileMenuRef = useRef(null);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        mobileMenuRef.current &&
        !mobileMenuRef.current.contains(event.target)
      ) {
        setActiveDropdown(null);
      }
    };

    document.addEventListener("click", handleClickOutside);
    return () => {
      document.removeEventListener("click", handleClickOutside);
    };
  }, []);

  const logout = async () => {
    const response = await api.post(
      import.meta.env.VITE_BASE_URL + "/api/logout",
      {}
    );
    if (response.status || response.status == 200) {
      navigate('/')
      api.removeTokenHeader();
      dispatch(handleLogout());
    }
  };

  if (isLoading) {
    return <Loading />;
  }


  return (
    <>
      {/* Main Header */}
      <header className="bg-white shadow-lg border-b border-gray-200 fixed top-0 left-0 w-full z-50">
        {/* Top Bar - Contact Info */}
        <div className="bg-gradient-to-r from-slate-50 to-gray-100 border-b border-gray-200 hidden lg:block">
          <div className="container mx-auto px-4 lg:px-6">
            <div className="flex items-center justify-between h-10 text-sm">
              <div className="flex items-center space-x-6">
                <div className="flex items-center space-x-2 text-gray-600">
                  <Icon icon="mdi:email-outline" className="w-4 h-4" />
                  <span>{organization?.email || "<EMAIL>"}</span>
                </div>
                <div className="flex items-center space-x-2 text-gray-600">
                  <Icon icon="mdi:map-marker-outline" className="w-4 h-4" />
                  <span>{organization?.address || "Your Location"}</span>
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <a
                  href={`tel:${organization?.hotline_number || "12345"}`}
                  className="flex items-center space-x-2 text-emerald-600 hover:text-emerald-700 font-medium transition-colors"
                >
                  <Icon icon="mdi:phone" className="w-4 h-4" />
                  <span>{organization?.hotline_number || "12345"}</span>
                </a>
              </div>
            </div>
          </div>
        </div>

        {/* Main Navigation */}
        <div className="container mx-auto px-4 lg:px-6">
          <div className="flex items-center h-16 lg:h-20 relative">
            {/* Logo Section - Fixed */}
            <div className="flex items-center flex-shrink-0 z-10">
              <Link to="/" className="flex items-center">
                <img
                  src={organization?.logo ? ASSET_URL + organization.logo : eduLogo}
                  alt="EduPack Logo"
                  className="h-10 lg:h-12 w-auto object-contain"
                />
              </Link>
            </div>

            {/* Search Container - Flexible */}
            <div className="flex-1 mx-4 relative">
              {/* Professional Search Bar - Desktop */}
              <div className="hidden lg:block">
                <ProfessionalSearch
                  placeholder="Search courses..."
                  onSearchStateChange={setIsSearchActive}
                />
              </div>

              {/* Desktop Navigation - Positioned over search when not active */}
              <nav className={`hidden lg:flex items-center space-x-1 transition-all duration-300 absolute right-0 top-1/2 transform -translate-y-1/2 ${
                isSearchActive ? 'opacity-0 pointer-events-none' : 'opacity-100'
              }`}>
                <Link
                  to="/"
                  className="px-4 py-2 text-gray-700 hover:text-sky-600 hover:bg-sky-50 rounded-lg font-medium transition-all duration-200 flex items-center whitespace-nowrap"
                >
                  <Icon icon="mdi:home" className="w-4 h-4 mr-2" />
                  Home
                </Link>

                <div className="flex items-center space-x-1">
                  <NavBar menuItems={menuItems?.data?.filter((item) => item.is_footer == false)} />
                </div>
              </nav>
            </div>

            {/* Right Side Actions - Fixed Position Container */}
            <div className="flex items-center space-x-3 flex-shrink-0 z-10 bg-white" style={{ minWidth: '200px' }}>
              {/* Search - Mobile */}
              <div className="lg:hidden flex-shrink-0">
                <ProfessionalSearch
                  placeholder="Search..."
                  className="w-36"
                />
              </div>

              {/* Notifications */}
              {isAuth && (
                <div className="relative flex-shrink-0">
                  <Notification />
                </div>
              )}

              {/* User Profile or Login - Fixed Width Container */}
              <div className="relative flex-shrink-0">
                {isAuth ? (
                  <div className="flex items-center">
                    {isAuth.user_type == "Student" ? (
                      <ProfileDropdown
                        label={isAuth.name.split(" ")[0]}
                        userImage={isAuth?.image ? ASSET_URL + isAuth.image : userImg}
                        onLogout={() => logout()}
                        classItem="hover:bg-sky-50 px-4 py-3 text-sm transition-colors rounded-lg"
                        classMenuItems="mt-2 w-[240px] mr-0 shadow-xl border border-gray-100 rounded-xl bg-white"
                        items={[
                          { name: "Profile", link: "/profile", icon: profileImg },
                          { name: "Dashboard", link: "/dashboard", icon: dashboardImg },
                          { name: "My Courses", link: "/my-courses", icon: myCoursesImg },
                          { name: "Assignments", link: "/my-assignments", icon: assignmentImg },
                          { name: "Exam Result", link: "/exam-result", icon: supportImg },
                          { name: "Live Classes", link: "student/live-class-list", icon: myCoursesImg },
                          { name: "My Payments", link: "/my-payments", icon: Payment },
                          { name: "Certificates", link: "/my-certificates", icon: '' },
                        ]}
                      />
                    ) : (
                      <ProfileDropdown
                        label={isAuth.name.split(" ")[0]}
                        userImage={isAuth?.image ? ASSET_URL + isAuth.image : userImg}
                        onLogout={() => logout()}
                        classItem="hover:bg-sky-50 px-4 py-3 text-sm transition-colors rounded-lg"
                        classMenuItems="mt-2 w-[240px] mr-0 shadow-xl border border-gray-100 rounded-xl bg-white"
                        items={[
                          { name: "Profile", link: "/profile", icon: profileImg },
                          { name: "Dashboard", link: "/mentor-dashboard", icon: dashboardImg },
                          { name: "Assignments", link: "/assignment-list", icon: assignmentImg },
                          { name: "Attendance", link: "/attendance-list", icon: profileImg },
                          { name: "Live Classes", link: "/live-class-list", icon: myCoursesImg },
                        ]}
                      />
                    )}
                  </div>
                ) : (
                  <div className="flex-shrink-0">
                    <Button
                      onClick={() =>
                        navigate("/login", {
                          state: { from: window.location.pathname },
                        })
                      }
                      className="bg-sky-600 hover:bg-sky-700 text-white px-5 py-2.5 rounded-lg font-medium shadow-sm hover:shadow-md transition-all duration-200 flex items-center space-x-2 whitespace-nowrap"
                    >
                      <Icon icon="mdi:login" className="w-4 h-4" />
                      <span>{t('layout.login')}</span>
                    </Button>
                  </div>
                )}
              </div>

              {/* Mobile Menu Button */}
              <div ref={mobileMenuRef} className="lg:hidden">
                <button
                  onClick={() =>
                    setActiveDropdown(activeDropdown === "mobile" ? null : "mobile")
                  }
                  className="p-2 text-gray-600 hover:text-sky-600 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  <Icon
                    icon={
                      activeDropdown === "mobile"
                        ? "mdi:close"
                        : "mdi:menu"
                    }
                    className="w-6 h-6 transition-all duration-300"
                  />
                </button>

                {/* Mobile Menu Dropdown */}
                {activeDropdown === "mobile" && (
                  <div className="absolute right-0 top-full mt-2 w-80 bg-white border border-gray-200 rounded-xl shadow-2xl py-2 z-50 max-h-[80vh] overflow-y-auto">
                    {/* Mobile Navigation Links */}
                    <div className="py-2">
                      <Link
                        to="/"
                        className="flex items-center px-4 py-3 text-gray-700 hover:bg-sky-50 hover:text-sky-600 transition-colors"
                        onClick={() => setActiveDropdown(null)}
                      >
                        <Icon icon="mdi:home" className="w-5 h-5 mr-3 text-gray-400" />
                        <span className="font-medium">Home</span>
                      </Link>

                      <div className="border-t border-gray-100 mt-2 pt-2">
                        {menuItems?.data?.map((item, index) =>
                          item.sub_categories.length > 0 ? (
                            <MenuDropdown
                              key={index}
                              label={item.name}
                              labelClass="flex items-center px-4 py-3 text-gray-700 hover:text-sky-600 hover:bg-sky-50 transition-colors font-medium"
                              classItem="hover:bg-sky-50 px-6 py-2 text-sm transition-colors text-gray-600 hover:text-sky-600"
                              items={item}
                            />
                          ) : (
                            <Link
                              key={index}
                              to={item.link}
                              className="flex items-center px-4 py-3 text-gray-700 hover:text-sky-600 hover:bg-sky-50 transition-colors font-medium"
                              onClick={() => setActiveDropdown(null)}
                            >
                              <Icon icon="mdi:circle-small" className="w-4 h-4 mr-2 text-gray-400" />
                              {item.name}
                            </Link>
                          )
                        )}
                      </div>
                    </div>

                    {/* Mobile Contact Info */}
                    <div className="px-4 py-3 border-t border-gray-100 bg-gray-50">
                      <a
                        href={`tel:${organization?.hotline_number || "12345"}`}
                        className="flex items-center space-x-3 text-emerald-600 hover:text-emerald-700 transition-colors"
                      >
                        <div className="p-2 bg-emerald-100 rounded-lg">
                          <Icon icon="mdi:phone" className="w-4 h-4" />
                        </div>
                        <div>
                          <p className="text-xs text-gray-500 uppercase tracking-wide">Call Us</p>
                          <p className="font-medium">{organization?.hotline_number || "12345"}</p>
                        </div>
                      </a>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Header Spacer */}
      <div className="h-16 lg:h-[90px]"></div>
    </>
  );
};

export default Header;
