import React, { useState, useEffect, useRef } from "react";
import { Icon } from "@iconify/react";
import eduLogo from "@/assets/images/logo/edu_logo.png";
import { useNavigate, Link } from "react-router-dom";
import Button from "@/components/ui/Button";
import useFetch from "@/hooks/useFetch";
import userImg from "@/assets/images/all-img/user5.jpeg";
import { useSelector, useDispatch } from "react-redux";
import { handleLogout } from "../../../pages/auth/common/store";
import { ASSET_URL } from "@/config";
import api from "@/server/api";
import MenuDropdown from "../../ui/MenuDropdown";
import NavBar from "../../ui/NavBar";
import SearchModal from "@/components/partials/header/Tools/SearchModal";
import Loading from "../../Loading";
import ProfileDropdown from "../../ui/ProfileDropdown";
import dashboardImg from '@/assets/images/svg/dashboard.svg';
import myCoursesImg from '@/assets/images/svg/myCourses.svg';
import profileImg from '@/assets/images/svg/profile.svg';
import assignmentImg from '@/assets/images/svg/assignment.svg';
import supportImg from '@/assets/images/svg/support.svg';
import Payment from '@/assets/images/svg/payment.svg';
import { useTranslation } from "react-i18next";
import Notification from "./Notification";

const Header = ({ organization }) => {
  const [activeDropdown, setActiveDropdown] = useState(null);
  const { isAuth } = useSelector((state) => state.auth);
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const { t, i18n } = useTranslation();
  const {
    data: menuItems,
    isLoading,
  } = useFetch({
    queryKey: "menu-list",
    endPoint: "menu-list",
  });


  const mobileMenuRef = useRef(null);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        mobileMenuRef.current &&
        !mobileMenuRef.current.contains(event.target)
      ) {
        setActiveDropdown(null);
      }
    };

    document.addEventListener("click", handleClickOutside);
    return () => {
      document.removeEventListener("click", handleClickOutside);
    };
  }, []);

  const logout = async () => {
    const response = await api.post(
      import.meta.env.VITE_BASE_URL + "/api/logout",
      {}
    );
    if (response.status || response.status == 200) {
      navigate('/')
      api.removeTokenHeader();
      dispatch(handleLogout());
    }
  };

  if (isLoading) {
    return <Loading />;
  }


  return (
    <>
      {/* Main Header */}
      <header className="bg-white shadow-lg border-b border-gray-200 fixed top-0 left-0 w-full z-50">
        {/* Top Bar - Contact Info */}
        <div className="bg-gradient-to-r from-slate-50 to-gray-100 border-b border-gray-200 hidden lg:block">
          <div className="container mx-auto px-4 lg:px-6">
            <div className="flex items-center justify-between h-10 text-sm">
              <div className="flex items-center space-x-6">
                <div className="flex items-center space-x-2 text-gray-600">
                  <Icon icon="mdi:email-outline" className="w-4 h-4" />
                  <span>{organization?.email || "<EMAIL>"}</span>
                </div>
                <div className="flex items-center space-x-2 text-gray-600">
                  <Icon icon="mdi:map-marker-outline" className="w-4 h-4" />
                  <span>{organization?.address || "Your Location"}</span>
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <a
                  href={`tel:${organization?.hotline_number || "12345"}`}
                  className="flex items-center space-x-2 text-emerald-600 hover:text-emerald-700 font-medium transition-colors"
                >
                  <Icon icon="mdi:phone" className="w-4 h-4" />
                  <span>{organization?.hotline_number || "12345"}</span>
                </a>
                {/* Language Switcher */}
                <Button
                  size="sm"
                  variant="outline"
                  className="h-8 px-3 text-xs border-gray-300 hover:border-sky-500 hover:text-sky-600 transition-colors"
                  onClick={() => {
                    i18n.changeLanguage(i18n.language == "bn" ? "en" : "bn");
                  }}
                >
                  <Icon icon="mdi:translate" className="w-3 h-3 mr-1" />
                  {i18n.language == "bn" ? "EN" : "বাং"}
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Main Navigation */}
        <div className="container mx-auto px-4 lg:px-6">
          <div className="flex items-center justify-between h-16 lg:h-20">
            {/* Logo Section */}
            <div className="flex items-center space-x-6">
              <Link to="/" className="flex items-center flex-shrink-0">
                <img
                  src={organization?.logo ? ASSET_URL + organization.logo : eduLogo}
                  alt="EduPack Logo"
                  className="h-12 lg:h-16 w-auto object-contain"
                />
              </Link>

              {/* Search Bar - Desktop */}
              <div className="hidden lg:block">
                <SearchModal />
              </div>
            </div>

            {/* Desktop Navigation */}
            <nav className="hidden lg:flex items-center space-x-8">
              <Link
                to="/"
                className="text-gray-700 hover:text-sky-600 font-medium transition-colors duration-200 relative group"
              >
                Home
                <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-sky-600 transition-all duration-200 group-hover:w-full"></span>
              </Link>

              <div className="flex items-center space-x-6">
                <NavBar menuItems={menuItems?.data?.filter((item) => item.is_footer == false)} />
              </div>
            </nav>

            {/* Right Side Actions */}
            <div className="flex items-center space-x-3">
              {/* Search Icon - Mobile */}
              <div className="lg:hidden">
                <SearchModal />
              </div>

              {/* Notifications */}
              {isAuth && (
                <div className="relative">
                  <Notification />
                </div>
              )}

              {/* User Profile or Login */}
              <div className="relative">
                {isAuth ? (
                  <div className="flex items-center">
                    {isAuth.user_type == "Student" ? (
                      <ProfileDropdown
                        label={isAuth.name.split(" ")[0]}
                        userImage={isAuth?.image ? ASSET_URL + isAuth.image : userImg}
                        onLogout={() => logout()}
                        classItem="hover:bg-sky-50 px-4 py-3 text-sm transition-colors"
                        classMenuItems="mt-3 w-[220px] mr-0 shadow-xl border-0 rounded-xl"
                        items={[
                          { name: "Profile", link: "/profile", icon: profileImg },
                          { name: "Dashboard", link: "/dashboard", icon: dashboardImg },
                          { name: "My Courses", link: "/my-courses", icon: myCoursesImg },
                          { name: "Assignments", link: "/my-assignments", icon: assignmentImg },
                          { name: "Exam Result", link: "/exam-result", icon: supportImg },
                          { name: "Live Classes", link: "student/live-class-list", icon: myCoursesImg },
                          { name: "My Payments", link: "/my-payments", icon: Payment },
                          { name: "Certificates", link: "/my-certificates", icon: '' },
                        ]}
                      />
                    ) : (
                      <ProfileDropdown
                        label={isAuth.name.split(" ")[0]}
                        userImage={isAuth?.image ? ASSET_URL + isAuth.image : userImg}
                        onLogout={() => logout()}
                        classItem="hover:bg-sky-50 px-4 py-3 text-sm transition-colors"
                        classMenuItems="mt-3 w-[220px] mr-0 shadow-xl border-0 rounded-xl"
                        items={[
                          { name: "Profile", link: "/profile", icon: profileImg },
                          { name: "Dashboard", link: "/mentor-dashboard", icon: dashboardImg },
                          { name: "Assignments", link: "/assignment-list", icon: assignmentImg },
                          { name: "Attendance", link: "/attendance-list", icon: profileImg },
                          { name: "Live Classes", link: "/live-class-list", icon: myCoursesImg },
                        ]}
                      />
                    )}
                  </div>
                ) : (
                  <Button
                    onClick={() =>
                      navigate("/login", {
                        state: { from: window.location.pathname },
                      })
                    }
                    className="bg-gradient-to-r from-sky-600 to-blue-600 hover:from-sky-700 hover:to-blue-700 text-white px-6 py-2.5 rounded-lg font-medium shadow-md hover:shadow-lg transition-all duration-200 transform hover:-translate-y-0.5"
                  >
                    <Icon icon="mdi:login" className="w-4 h-4 mr-2" />
                    {t('layout.login')}
                  </Button>
                )}
              </div>

              {/* Mobile Menu Button */}
              <div ref={mobileMenuRef} className="lg:hidden">
                <button
                  onClick={() =>
                    setActiveDropdown(activeDropdown === "mobile" ? null : "mobile")
                  }
                  className="p-2 text-gray-700 hover:text-sky-600 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  <Icon
                    icon={
                      activeDropdown === "mobile"
                        ? "mdi:close"
                        : "mdi:menu"
                    }
                    className="w-6 h-6 transition-all duration-300"
                  />
                </button>

                {/* Mobile Menu Dropdown */}
                {activeDropdown === "mobile" && (
                  <div className="absolute right-0 top-full mt-2 w-80 bg-white border border-gray-200 rounded-xl shadow-xl py-4 z-50">
                    {/* Mobile Search */}
                    <div className="px-4 pb-4 border-b border-gray-100">
                      <SearchModal />
                    </div>

                    {/* Mobile Navigation Links */}
                    <div className="py-2">
                      <Link
                        to="/"
                        className="block px-4 py-3 text-gray-700 hover:bg-sky-50 hover:text-sky-600 transition-colors"
                        onClick={() => setActiveDropdown(null)}
                      >
                        <Icon icon="mdi:home" className="w-5 h-5 inline mr-3" />
                        Home
                      </Link>

                      <div className="px-4">
                        {menuItems?.data?.map((item, index) =>
                          item.sub_categories.length > 0 ? (
                            <MenuDropdown
                              key={index}
                              label={item.name}
                              labelClass="py-3 text-gray-700 hover:text-sky-600"
                              classItem="hover:bg-sky-50 px-4 py-2 text-sm transition-colors"
                              items={item}
                            />
                          ) : (
                            <Link
                              key={index}
                              to={item.link}
                              className="block py-3 text-gray-700 hover:text-sky-600 transition-colors"
                              onClick={() => setActiveDropdown(null)}
                            >
                              {item.name}
                            </Link>
                          )
                        )}
                      </div>
                    </div>

                    {/* Mobile Contact Info */}
                    <div className="px-4 pt-4 border-t border-gray-100">
                      <a
                        href={`tel:${organization?.hotline_number || "12345"}`}
                        className="flex items-center space-x-3 text-emerald-600 hover:text-emerald-700 transition-colors"
                      >
                        <Icon icon="mdi:phone" className="w-5 h-5" />
                        <span className="font-medium">{organization?.hotline_number || "12345"}</span>
                      </a>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Header Spacer */}
      <div className="h-16 lg:h-[90px]"></div>
    </>
  );
};

export default Header;
