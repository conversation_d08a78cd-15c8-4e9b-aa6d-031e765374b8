import React from "react";
import { NavLink, useParams } from "react-router-dom";
import { Icon } from "@iconify/react";

const NavBar = ({ menuItems }) => {
  const primaryCategories = menuItems?.slice(0, 5); // Show more primary categories
  const moreCategories = menuItems?.slice(5); // Remaining categories for "More"

  const { menuId, subMenuId } = useParams(); // Get IDs from URL parameters

  return (
    <>
      {/* Primary Categories */}
      {primaryCategories?.map((category) => {
        const isActive = menuId == category.id; // Check if the category is active
        return (
          <div className="relative group" key={category.id}>
            <NavLink
              to={category?.is_custom_page ? `/${category.slug}` : `/courses/${category.id}`}
              className={`flex items-center gap-1 px-4 py-2 text-gray-700 hover:text-sky-600 hover:bg-sky-50 rounded-lg font-medium transition-all duration-200 ${
                isActive ? "text-sky-600 bg-sky-50 font-semibold" : ""
              }`}
            >
              {category.name}
              {category.has_submenu && category.sub_categories?.length > 0 && (
                <Icon
                  icon="mdi:chevron-down"
                  className={`w-4 h-4 transition-transform duration-200 ${
                    isActive ? "rotate-180" : "group-hover:rotate-180"
                  }`}
                />
              )}
            </NavLink>

            {category.has_submenu && category.sub_categories?.length > 0 && (
              <div className="absolute left-0 top-full mt-2 hidden group-hover:block bg-white border border-gray-200 rounded-xl shadow-xl w-64 py-2 z-50">
                <div className="px-3 py-2 border-b border-gray-100">
                  <h4 className="text-sm font-semibold text-gray-800">{category.name}</h4>
                </div>
                {category.sub_categories.map((sub) => {
                  const isSubActive = isActive && subMenuId == sub.id;
                  return (
                    <NavLink
                      key={sub.id}
                      to={`/courses/${category.id}/${sub.id}`}
                      className={`block px-4 py-3 text-sm hover:bg-sky-50 hover:text-sky-600 transition-colors ${
                        isSubActive ? "bg-sky-50 text-sky-600 font-medium" : "text-gray-700"
                      }`}
                    >
                      <div className="flex items-center">
                        <Icon icon="mdi:circle-small" className="w-4 h-4 mr-1 text-gray-400" />
                        {sub.name}
                      </div>
                    </NavLink>
                  );
                })}
              </div>
            )}
          </div>
        );
      })}

      {/* More Dropdown */}
      {moreCategories?.length > 0 && (
        <div className="relative group">
          <span className="flex items-center gap-1 cursor-pointer px-4 py-2 text-gray-700 hover:text-sky-600 hover:bg-sky-50 rounded-lg font-medium transition-all duration-200">
            More
            <Icon icon="mdi:chevron-down" className="w-4 h-4 transition-transform duration-200 group-hover:rotate-180" />
          </span>

          <div className="absolute left-0 top-full mt-2 hidden group-hover:block bg-white border border-gray-200 rounded-xl shadow-xl w-72 py-2 z-50">
            <div className="px-3 py-2 border-b border-gray-100">
              <h4 className="text-sm font-semibold text-gray-800">More Categories</h4>
            </div>
            {moreCategories?.map((category) => {
              const isActive = menuId == category.id;
              return (
                <div className="relative group/sub" key={category.id}>
                  <NavLink
                    to={category?.is_custom_page ? `/${category.slug}` : `/courses/${category.id}`}
                    className={`flex items-center justify-between px-4 py-3 text-sm hover:bg-sky-50 hover:text-sky-600 transition-colors ${
                      isActive ? "bg-sky-50 text-sky-600 font-medium" : "text-gray-700"
                    }`}
                  >
                    <div className="flex items-center">
                      <Icon icon="mdi:circle-small" className="w-4 h-4 mr-1 text-gray-400" />
                      {category.name}
                    </div>
                    {category.sub_categories?.length > 0 && (
                      <Icon icon="mdi:chevron-right" className="w-4 h-4 text-gray-400" />
                    )}
                  </NavLink>

                  {category.has_submenu && category.sub_categories?.length > 0 && (
                    <div className="absolute left-full top-0 ml-1 hidden group-hover/sub:block bg-white border border-gray-200 rounded-xl shadow-xl w-64 py-2 z-50">
                      <div className="px-3 py-2 border-b border-gray-100">
                        <h4 className="text-sm font-semibold text-gray-800">{category.name}</h4>
                      </div>
                      {category.sub_categories.map((sub) => {
                        const isSubActive = isActive && subMenuId == sub.id;
                        return (
                          <NavLink
                            key={sub.id}
                            to={`/courses/${category.id}/${sub.id}`}
                            className={`block px-4 py-3 text-sm hover:bg-sky-50 hover:text-sky-600 transition-colors ${
                              isSubActive ? "bg-sky-50 text-sky-600 font-medium" : "text-gray-700"
                            }`}
                          >
                            <div className="flex items-center">
                              <Icon icon="mdi:circle-small" className="w-4 h-4 mr-1 text-gray-400" />
                              {sub.name}
                            </div>
                          </NavLink>
                        );
                      })}
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      )}
    </>
  );
};

export default NavBar;
