import React from "react";
import { NavLink, useParams } from "react-router-dom";
import { Icon } from "@iconify/react";

const NavBar = ({ menuItems }) => {
  // Organize menu items intelligently based on your categories
  const organizeMenuItems = (items) => {
    if (!items || items.length === 0) return { primary: [], secondary: [] };

    // Define priority categories that should always be visible
    const priorityCategories = [
      'freelancing courses',
      'language courses',
      'skill development',
      'design & arts'
    ];

    const primary = [];
    const secondary = [];

    // First, add priority categories to primary
    priorityCategories.forEach(priority => {
      const found = items.find(item =>
        item.name.toLowerCase().includes(priority.toLowerCase()) ||
        priority.toLowerCase().includes(item.name.toLowerCase())
      );
      if (found && !primary.includes(found)) {
        primary.push(found);
      }
    });

    // Then add remaining items, limiting primary to 3 total
    items.forEach(item => {
      if (!primary.includes(item)) {
        if (primary.length < 3) {
          primary.push(item);
        } else {
          secondary.push(item);
        }
      }
    });

    return { primary, secondary };
  };

  const { primary: primaryCategories, secondary: moreCategories } = organizeMenuItems(menuItems);
  const { menuId, subMenuId } = useParams(); // Get IDs from URL parameters

  return (
    <>
      {/* Primary Categories */}
      {primaryCategories?.map((category) => {
        const isActive = menuId == category.id; // Check if the category is active
        return (
          <div className="relative group" key={category.id}>
            <NavLink
              to={category?.is_custom_page ? `/${category.slug}` : `/courses/${category.id}`}
              className={`flex items-center gap-1 px-2 lg:px-3 py-2 text-gray-700 hover:text-sky-600 hover:bg-sky-50 rounded-lg font-medium transition-all duration-200 whitespace-nowrap text-sm ${
                isActive ? "text-sky-600 bg-sky-50 font-semibold" : ""
              }`}
            >
              <span className="whitespace-nowrap">{category.name}</span>
              {category.has_submenu && category.sub_categories?.length > 0 && (
                <Icon
                  icon="mdi:chevron-down"
                  className={`w-3 h-3 lg:w-4 lg:h-4 transition-transform duration-200 ${
                    isActive ? "rotate-180" : "group-hover:rotate-180"
                  }`}
                />
              )}
            </NavLink>

            {category.has_submenu && category.sub_categories?.length > 0 && (
              <div className="absolute left-0 top-full hidden group-hover:block bg-white border border-gray-200 rounded-xl shadow-xl w-72 py-3 z-50">
                <div className="px-4 py-2 border-b border-gray-100">
                  <h4 className="text-sm font-semibold text-gray-800 flex items-center whitespace-nowrap">
                    <Icon icon="mdi:school" className="w-4 h-4 mr-2 text-sky-600" />
                    {category.name}
                  </h4>
                  <p className="text-xs text-gray-500 mt-1">Choose your specialization</p>
                </div>
                <div className="max-h-80 overflow-y-auto">
                  {category.sub_categories.map((sub) => {
                    const isSubActive = isActive && subMenuId == sub.id;
                    return (
                      <NavLink
                        key={sub.id}
                        to={`/courses/${category.id}/${sub.id}`}
                        className={`block px-4 py-3 text-sm hover:bg-sky-50 hover:text-sky-600 transition-colors group/item ${
                          isSubActive ? "bg-sky-50 text-sky-600 font-medium" : "text-gray-700"
                        }`}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <Icon icon="mdi:play-circle-outline" className="w-4 h-4 mr-2 text-gray-400 group-hover/item:text-sky-500" />
                            <span className="whitespace-nowrap">{sub.name}</span>
                          </div>
                          <Icon icon="mdi:arrow-right" className="w-3 h-3 text-gray-300 group-hover/item:text-sky-500" />
                        </div>
                      </NavLink>
                    );
                  })}
                </div>
              </div>
            )}
          </div>
        );
      })}

      {/* More Dropdown */}
      {moreCategories?.length > 0 && (
        <div className="relative group">
          <span className="flex items-center gap-1 cursor-pointer px-2 lg:px-3 py-2 text-gray-700 hover:text-sky-600 hover:bg-sky-50 rounded-lg font-medium transition-all duration-200 whitespace-nowrap text-sm">
            <Icon icon="mdi:dots-horizontal" className="w-3 h-3 lg:w-4 lg:h-4 mr-1" />
            <span className="whitespace-nowrap">More</span>
            <Icon icon="mdi:chevron-down" className="w-3 h-3 lg:w-4 lg:h-4 transition-transform duration-200 group-hover:rotate-180" />
          </span>

          <div className="absolute left-0 top-full hidden group-hover:block bg-white border border-gray-200 rounded-xl shadow-xl w-80 py-3 z-50 max-h-96 overflow-y-auto">
            <div className="px-4 py-2 border-b border-gray-100">
              <h4 className="text-sm font-semibold text-gray-800 flex items-center">
                <Icon icon="mdi:school-outline" className="w-4 h-4 mr-2 text-sky-600" />
                Additional Course Categories
              </h4>
              <p className="text-xs text-gray-500 mt-1">Explore more learning opportunities</p>
            </div>

            <div className="grid grid-cols-1 gap-1 p-2">
              {moreCategories?.map((category) => {
                const isActive = menuId == category.id;
                return (
                  <div className="relative group/sub" key={category.id}>
                    <NavLink
                      to={category?.is_custom_page ? `/${category.slug}` : `/courses/${category.id}`}
                      className={`flex items-center justify-between px-3 py-2.5 text-sm hover:bg-sky-50 hover:text-sky-600 transition-colors rounded-lg group/item ${
                        isActive ? "bg-sky-50 text-sky-600 font-medium" : "text-gray-700"
                      }`}
                    >
                      <div className="flex items-center">
                        <Icon icon="mdi:book-open-variant" className="w-4 h-4 mr-3 text-gray-400 group-hover/item:text-sky-500" />
                        <div>
                          <div className="font-medium whitespace-nowrap">{category.name}</div>
                          {category.sub_categories?.length > 0 && (
                            <div className="text-xs text-gray-500 whitespace-nowrap">
                              {category.sub_categories.length} specialization{category.sub_categories.length > 1 ? 's' : ''}
                            </div>
                          )}
                        </div>
                      </div>
                      {category.sub_categories?.length > 0 && (
                        <Icon icon="mdi:chevron-right" className="w-4 h-4 text-gray-400 group-hover/item:text-sky-500" />
                      )}
                    </NavLink>

                    {category.has_submenu && category.sub_categories?.length > 0 && (
                      <div className="absolute left-full top-0 hidden group-hover/sub:block bg-white border border-gray-200 rounded-xl shadow-xl w-72 py-3 z-50">
                        <div className="px-4 py-2 border-b border-gray-100">
                          <h4 className="text-sm font-semibold text-gray-800 flex items-center whitespace-nowrap">
                            <Icon icon="mdi:school" className="w-4 h-4 mr-2 text-sky-600" />
                            {category.name}
                          </h4>
                          <p className="text-xs text-gray-500 mt-1">Choose your specialization</p>
                        </div>
                        <div className="max-h-64 overflow-y-auto">
                          {category.sub_categories.map((sub) => {
                            const isSubActive = isActive && subMenuId == sub.id;
                            return (
                              <NavLink
                                key={sub.id}
                                to={`/courses/${category.id}/${sub.id}`}
                                className={`block px-4 py-3 text-sm hover:bg-sky-50 hover:text-sky-600 transition-colors group/subitem ${
                                  isSubActive ? "bg-sky-50 text-sky-600 font-medium" : "text-gray-700"
                                }`}
                              >
                                <div className="flex items-center justify-between">
                                  <div className="flex items-center">
                                    <Icon icon="mdi:play-circle-outline" className="w-4 h-4 mr-2 text-gray-400 group-hover/subitem:text-sky-500" />
                                    <span className="whitespace-nowrap">{sub.name}</span>
                                  </div>
                                  <Icon icon="mdi:arrow-right" className="w-3 h-3 text-gray-300 group-hover/subitem:text-sky-500" />
                                </div>
                              </NavLink>
                            );
                          })}
                        </div>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default NavBar;
