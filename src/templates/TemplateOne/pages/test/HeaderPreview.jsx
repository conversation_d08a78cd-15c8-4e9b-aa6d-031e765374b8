import React from "react";
import Header from "../../components/partials/header";
import { useSelector } from "react-redux";

const HeaderPreview = () => {
  const { organization } = useSelector((state) => state.common);

  return (
    <div className="min-h-screen bg-gray-50">
      <Header organization={organization} />
      
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <h1 className="text-4xl font-bold text-gray-800 mb-6">
              Template One Header Preview
            </h1>
            
            <div className="space-y-6">
              <div className="bg-gradient-to-r from-sky-50 to-blue-50 p-6 rounded-lg border border-sky-200">
                <h2 className="text-2xl font-semibold text-sky-800 mb-4">
                  🎉 Professional Header Features
                </h2>
                <div className="grid md:grid-cols-2 gap-4">
                  <div className="space-y-3">
                    <h3 className="font-semibold text-gray-800">✨ Design Features:</h3>
                    <ul className="space-y-2 text-sm text-gray-600">
                      <li>• Two-tier header design with top contact bar</li>
                      <li>• Professional gradient backgrounds</li>
                      <li>• Smooth hover animations and transitions</li>
                      <li>• Modern shadow and border styling</li>
                      <li>• Responsive mobile-first design</li>
                    </ul>
                  </div>
                  <div className="space-y-3">
                    <h3 className="font-semibold text-gray-800">🚀 Functionality:</h3>
                    <ul className="space-y-2 text-sm text-gray-600">
                      <li>• Enhanced navigation with better dropdowns</li>
                      <li>• Improved search modal integration</li>
                      <li>• Professional profile dropdown</li>
                      <li>• Mobile-optimized hamburger menu</li>
                      <li>• Language switcher in top bar</li>
                    </ul>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 p-6 rounded-lg">
                <h3 className="text-lg font-semibold text-gray-800 mb-4">Header Structure:</h3>
                <div className="space-y-3 text-sm">
                  <div className="bg-white p-3 rounded border-l-4 border-gray-400">
                    <strong>Top Bar (Desktop only):</strong> Contact info, email, phone, language switcher
                  </div>
                  <div className="bg-white p-3 rounded border-l-4 border-sky-500">
                    <strong>Main Header:</strong> Logo, search, navigation, notifications, user profile
                  </div>
                  <div className="bg-white p-3 rounded border-l-4 border-green-500">
                    <strong>Mobile Menu:</strong> Collapsible navigation with search and contact info
                  </div>
                </div>
              </div>

              <div className="bg-yellow-50 border border-yellow-200 p-6 rounded-lg">
                <h3 className="text-lg font-semibold text-yellow-800 mb-3">
                  🔧 How to Switch to Template One
                </h3>
                <p className="text-yellow-700 text-sm mb-3">
                  To use this header design in your organization:
                </p>
                <ol className="list-decimal list-inside space-y-2 text-sm text-yellow-700">
                  <li>Go to your organization settings</li>
                  <li>Select "Template One" as your active template</li>
                  <li>Save the changes</li>
                  <li>The new professional header will be applied site-wide</li>
                </ol>
              </div>

              <div className="text-center pt-8">
                <p className="text-gray-600">
                  This preview shows the new professional header design for Template One.
                  <br />
                  The header includes modern styling, better navigation, and improved user experience.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HeaderPreview;
